import { updateShippingCost, updateFulfillmentCost } from "../models/ShippingCost.server";
import { getStoreMappingByStoreId } from "../models/ShipStationStoreMapping.server";
import { hasProcessedWebhook, markWebhookProcessed } from "../models/ProcessedWebhook.server";
import axios from "axios";
import https from "https";

// This file is for handling of ShipStation webhooks
// Webhooks should be verified, deduplicated, and periodically reconcicled with synced data
// TODO - Replace all deprecated json calls
// TODO - Ensure all these transmissions are encrypted properly

export async function action({ request }) {
  if (request.method !== "POST") {
    return Response.json({ error: "Method not allowed" }, { status: 405 });
  }

  try {
    // Check for API key for authentication
    const apiKey = request.headers.get("X-API-Key");
    if (apiKey && apiKey !== process.env.SHIPSTATION_WEBHOOK_KEY) {
      return Response.json({ error: "Invalid API key" }, { status: 401 });
    }

    const payload = await request.json();
    console.log('Received ShipStation webhook:', JSON.stringify(payload));

    // Extract a unique identifier for this webhook
    // Use resource_url if available, or a combination of other fields
    let webhookId = '';
    if (payload.resource_url) {
      webhookId = `shipstation-${payload.resource_url}`;
    } else if (payload.resource_id) {
      webhookId = `shipstation-${payload.resource_id}`;
    } else if (payload.resource_data && payload.resource_data.shipmentId) {
      webhookId = `shipstation-shipment-${payload.resource_data.shipmentId}`;
    } else {
      // If we can't find a reliable ID, create one from the payload
      webhookId = `shipstation-${Date.now()}-${Math.random().toString(36).substring(2, 15)}`;
    }

    console.log(`Generated webhook ID: ${webhookId}`);


    // Try to extract store ID from the webhook payload for logging
    let storeIdFromPayload = null;

    if (payload.resource_url) {
      try {
        const url = new URL(payload.resource_url);
        const params = new URLSearchParams(url.search);

        // Extract store ID - this is what we're primarily interested in
        storeIdFromPayload = params.get('storeId') || params.get('storeID') || params.get('storeid');
        if (storeIdFromPayload) {
          console.log(`SHIPSTATION WEBHOOK - Store ID from URL: ${storeIdFromPayload}`);
        } else {
          // Only log all parameters if we couldn't find the store ID
          console.log(`Webhook URL parameters: ${url.search}`);
          for (const [key, value] of params.entries()) {
            console.log(`Webhook URL parameter: ${key}=${value}`);
          }
        }
      } catch (urlError) {
        console.error(`Error extracting store ID from URL: ${urlError.message}`);
      }
    } else if (payload.resource_data) {
      // Extract store ID from resource_data
      if (payload.resource_data.storeId) {
        storeIdFromPayload = payload.resource_data.storeId;
        console.log(`SHIPSTATION WEBHOOK - Store ID from resource_data: ${storeIdFromPayload}`);
      } else {
        // Only log all keys if we couldn't find the store ID
        console.log(`Webhook resource_data keys: ${Object.keys(payload.resource_data).join(', ')}`);
      }
    }


    // Check if this webhook has already been processed
    try {
      const alreadyProcessed = await hasProcessedWebhook(storeIdFromPayload, webhookId);
      if (alreadyProcessed) {
        console.log(`Webhook ${webhookId} has already been processed. Skipping.`);
        return Response.json({
          success: true,
          message: 'Webhook already processed'
        }, { status: 200 });
      }
    } catch (error) {
      // If there's an error checking the webhook status, log it but continue processing
      console.error(`Error checking webhook status: ${error.message}`);
      // Don't return here, continue processing the webhook
    }

    // Check if this is a shipment notification
    if (payload.resource_type === 'SHIP_NOTIFY' || payload.event === 'SHIP_NOTIFY') {
      console.log('Processing SHIP_NOTIFY webhook');

      // Check if we have a resource_url to fetch the shipment data
      if (payload.resource_url) {
        console.log(`Fetching shipment data from resource_url: ${payload.resource_url}`);

        // Try to extract the store ID and other info from the resource URL
        let storeIdFromUrl = null;
        let resourceDomain = null;
        try {
          const url = new URL(payload.resource_url);
          resourceDomain = url.hostname;
          const params = new URLSearchParams(url.search);
          storeIdFromUrl = params.get('storeId') || params.get('storeID') || params.get('storeid');

          console.log(`Resource URL domain: ${resourceDomain}`);

          // Verify that the URL is from ShipStation
          if (!resourceDomain.includes('shipstation.com')) {
            console.warn(`Resource URL domain does not appear to be from ShipStation: ${resourceDomain}`);
          }

          if (storeIdFromUrl) {
            // Convert to string to ensure consistent type
            storeIdFromUrl = String(storeIdFromUrl);
            console.log(`SHIPSTATION WEBHOOK - Store ID from resource URL: ${storeIdFromUrl}`);
            // Save the store ID for later use
            process.env.TEMP_STORE_ID = storeIdFromUrl;
          } else {
            // Only log all parameters if we couldn't find the store ID
            console.log(`Resource URL parameters: ${url.search}`);
            for (const [key, value] of params.entries()) {
              console.log(`Resource URL parameter: ${key}=${value}`);
            }
          }
        } catch (urlError) {
          console.error(`Error parsing resource URL: ${urlError.message}`);
        }

        try {
          // Fetch the shipment data from the resource_url
          // For ShipStation webhooks, we need to use the v1 API key and secret
          const API_KEY = process.env.SHIPSTATION_V1_KEY;
          const API_SECRET = process.env.SHIPSTATION_V1_SECRET;

          if (!API_KEY || !API_SECRET) {
            throw new Error("ShipStation v1 API key and secret are not configured");
          }

          console.log('Using ShipStation v1 API credentials: [REDACTED]');

          // Set up axios with proper TLS configuration
          const httpsAgent = new https.Agent({
            rejectUnauthorized: true, // Verify SSL certificates
            minVersion: 'TLSv1.1' // Require TLS v1.1 or higher
          });

          // Create Basic Auth header with v1 API key and secret
          const basicAuth = Buffer.from(`${API_KEY}:${API_SECRET}`).toString('base64');

          let response;
          let authMethod = '';

          // Use Basic Auth with v1 API key and secret
          try {
            console.log('Using Basic Auth with v1 API credentials');
            response = await axios.get(payload.resource_url, {
              headers: {
                'Authorization': `Basic ${basicAuth}`,
                'Content-Type': 'application/json'
              },
              httpsAgent: httpsAgent
            });
            console.log(`Successfully fetched shipment data with Basic Auth`);
            authMethod = 'Basic Auth with v1 credentials';

            // If we got here, we have a successful response
            console.log(`Successfully authenticated with ${authMethod}`);
            console.log(`Response data preview: ${JSON.stringify(response.data).substring(0, 200)}...`);

            // Get the response data
            const responseData = response.data;

            // Log the full response structure to understand what's available
            console.log('Full response structure:', Object.keys(responseData));

            // Look for store ID in the response data
            let storeIdFromResponse = null;

            // Check for store ID in different possible locations in the response
            if (responseData.storeId) {
                storeIdFromResponse = responseData.storeId;
                console.log(`SHIPSTATION WEBHOOK - Found store ID in response data: ${storeIdFromResponse}`);
            } else if (responseData.store && responseData.store.storeId) {
                storeIdFromResponse = responseData.store.storeId;
                console.log(`SHIPSTATION WEBHOOK - Found store ID in response store object: ${storeIdFromResponse}`);
            } else if (responseData.advancedOptions && responseData.advancedOptions.storeId) {
                storeIdFromResponse = responseData.advancedOptions.storeId;
                console.log(`SHIPSTATION WEBHOOK - Found store ID in response advancedOptions: ${storeIdFromResponse}`);
            }

            // If we have shipments array, check the first one for store ID
            if (!storeIdFromResponse && responseData.shipments && responseData.shipments.length > 0) {
                const firstShipment = responseData.shipments[0];
                if (firstShipment.storeId) {
                    storeIdFromResponse = firstShipment.storeId;
                    console.log(`SHIPSTATION WEBHOOK - Found store ID in first shipment: ${storeIdFromResponse}`);
                } else if (firstShipment.store && firstShipment.store.storeId) {
                    storeIdFromResponse = firstShipment.store.storeId;
                    console.log(`SHIPSTATION WEBHOOK - Found store ID in first shipment's store object: ${storeIdFromResponse}`);
                }
            }

            // Process each shipment in the response
            const shipments = responseData.shipments || [responseData];

            if (shipments.length === 0) {
              console.log('No shipments found in the response');
              return Response.json({
                success: false,
                error: 'No shipments found in the response'
              }, { status: 400 });
            }

            console.log(`Found ${shipments.length} shipments to process`);

            // Process each shipment
            for (const shipment of shipments) {
              // Log the shipment structure
              console.log('Shipment structure:', Object.keys(shipment));

              // If we have a storeId from the response, add it to the shipment
              if (storeIdFromResponse) {
                // Ensure it's a string
                shipment.storeId = String(storeIdFromResponse);
                console.log(`SHIPSTATION WEBHOOK - Added store ID to shipment: ${shipment.storeId}`);
              } else if (shipment.storeId) {
                // If the shipment already has a store ID, log it prominently
                console.log(`SHIPSTATION WEBHOOK - Shipment already has store ID: ${shipment.storeId}`);
              } else {
                // Log that we couldn't find a store ID
                console.log(`SHIPSTATION WEBHOOK - No store ID found for this shipment`);
              }

              try {
                await processShipment(shipment, storeIdFromPayload, webhookId);
              } catch (shipmentError) {
                console.error(`Error processing shipment: ${shipmentError.message}`);
                // Continue with next shipment instead of failing the entire batch
              }
            }

            // Mark the webhook as processed
            try {
              await markWebhookProcessed(storeIdFromPayload, 'SHIP_NOTIFY', webhookId);
              console.log(`Marked webhook ${webhookId} as processed`);
            } catch (markError) {
              console.error(`Error marking webhook as processed: ${markError.message}`);
              // Continue even if marking fails
            }

            return Response.json({
              success: true,
              message: `Processed ${shipments.length} shipments`
            });
          } catch (error) {
            // This catch block handles errors from all authentication methods
            console.error(`Error fetching shipment data: ${error.message}`);

            // Log more detailed error information
            if (error.response) {
              // The request was made and the server responded with a status code
              // that falls out of the range of 2xx
              console.error(`Response status: ${error.response.status}`);
              console.error(`Response headers: ${JSON.stringify(error.response.headers)}`);
              console.error(`Response data: ${JSON.stringify(error.response.data)}`);
            } else if (error.request) {
              // The request was made but no response was received
              console.error(`No response received: ${error.request}`);
            } else {
              // Something happened in setting up the request that triggered an Error
              console.error(`Error setting up request: ${error.message}`);
            }

            return Response.json({
              success: false,
              error: `Error fetching shipment data: ${error.message}`,
              details: error.response ? error.response.data : null
            }, { status: 500 });
          }
        } catch (error) {
          console.error(`Error in resource URL processing: ${error.message}`);
          return Response.json({
            success: false,
            error: `Error in resource URL processing: ${error.message}`
          }, { status: 500 });
        }
      } else {
        // If no resource_url, try to process the payload directly
        console.log('No resource_url found, processing payload directly');
        const shipment = payload.resource_data || payload;
        await processShipment(shipment, storeIdFromPayload, webhookId);

        // Mark the webhook as processed
        try {
          await markWebhookProcessed(storeIdFromPayload, 'SHIP_NOTIFY', webhookId);
          console.log(`Marked webhook ${webhookId} as processed`);
        } catch (markError) {
          console.error(`Error marking webhook as processed: ${markError.message}`);
          // Continue even if marking fails
        }

        return Response.json({
          success: true,
          message: 'Processed shipment from payload'
        });
      }
    } else {
      // Not a shipment notification
      return Response.json({
        success: true,
        message: "Ignored non-shipment webhook"
      });
    }
  } catch (error) {
    console.error(`Error processing ShipStation webhook: ${error.message}`);
    return Response.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}

/**
 * Process a shipment from ShipStation
 * @param {object} shipment - The shipment data
 * @param {string} storeIdFromPayload - The temporary shop identifier
 * @param {string} webhookId - The unique webhook ID
 * @returns {Promise<void>}
 */
async function processShipment(shipment, storeIdFromPayload, webhookId) {
  console.log(`Processing shipment: ${JSON.stringify(shipment).substring(0, 200)}...`);

  // Log detailed shipment information - focusing on key identifiers
  console.log('Shipment keys:', Object.keys(shipment));
  if (shipment.shipmentId) console.log(`Shipment ID: ${shipment.shipmentId}`);
  if (shipment.orderId) console.log(`Order ID: ${shipment.orderId}`);
  if (shipment.orderNumber) console.log(`Order Number: ${shipment.orderNumber}`);
  if (shipment.createDate) console.log(`Create Date: ${shipment.createDate}`);
  if (shipment.shipDate) console.log(`Ship Date: ${shipment.shipDate}`);

  // Log carrier_fee if it exists
  if (shipment.carrier_fee) console.log(`Carrier Fee: ${JSON.stringify(shipment.carrier_fee)}`);

  // Check for store information - log prominently with consistent prefix
  if (shipment.storeId) console.log(`SHIPSTATION WEBHOOK - Store ID direct: ${shipment.storeId}`);
  if (shipment.store_id) console.log(`SHIPSTATION WEBHOOK - Store ID underscore: ${shipment.store_id}`);
  if (shipment.storeID) console.log(`SHIPSTATION WEBHOOK - Store ID caps: ${shipment.storeID}`);

  // Check for store in advanced options
  if (shipment.advancedOptions && shipment.advancedOptions.storeId) {
    console.log(`SHIPSTATION WEBHOOK - Store ID in advanced options: ${shipment.advancedOptions.storeId}`);
  }

  // Check for store object
  if (shipment.store && shipment.store.storeId) {
    console.log(`SHIPSTATION WEBHOOK - Store ID in store object: ${shipment.store.storeId}`);
    if (shipment.store.storeName) console.log(`SHIPSTATION WEBHOOK - Store name: ${shipment.store.storeName}`);
  }

  // Extract the store ID from the shipment
  let storeId = shipment.storeId || shipment.store_id || shipment.storeID;

  // Check for store ID in other possible locations
  if (!storeId && shipment.advancedOptions && shipment.advancedOptions.storeId) {
    storeId = shipment.advancedOptions.storeId;
    console.log(`SHIPSTATION WEBHOOK - Found store ID in advancedOptions: ${storeId}`);
  }

  // Check for store ID in store object
  if (!storeId && shipment.store && shipment.store.storeId) {
    storeId = shipment.store.storeId;
    console.log(`SHIPSTATION WEBHOOK - Found store ID in store object: ${storeId}`);
  }

  // Check for store ID in order object
  if (!storeId && shipment.order && shipment.order.storeId) {
    storeId = shipment.order.storeId;
    console.log(`SHIPSTATION WEBHOOK - Found store ID in order object: ${storeId}`);
  }

  // If still no store ID, try to use the store ID from the URL
  if (!storeId) {
    // Check for the store ID we extracted from the URL
    storeId = process.env.TEMP_STORE_ID;
    if (storeId) {
      console.log(`SHIPSTATION WEBHOOK - Using store ID from resource URL: ${storeId}`);
    } else {
      console.error('SHIPSTATION WEBHOOK - No store ID found in shipment');
      throw new Error('No store ID found in shipment');
    }
  }

  // Ensure store ID is a string
  storeId = String(storeId);
  console.log(`SHIPSTATION WEBHOOK - FINAL STORE ID: ${storeId}`);

  // Check if the store ID is numeric
  if (!isNaN(storeId)) {
    console.log(`Store ID is numeric: ${storeId}`);
  } else {
    console.log(`Store ID is not numeric: ${storeId}`);
  }

  // Get the store mapping from the database
  const storeMapping = await getStoreMappingByStoreId(storeId);

  // If no mapping found, return early - there will never be a default shop
  if (!storeMapping) {
    console.warn(`No mapping found for store ID: ${storeId}`);
    // Return early with a message indicating that a store mapping needs to be configured
    return {
      success: true,
      message: `Webhook received for store ID: ${storeId}, but no mapping configured. Please configure a store mapping for this store ID.`
    };
  }

  // Get the shop from the store mapping
  const shop = storeMapping.shop;

  // Log the shipment structure for debugging
  console.log(`Shipment structure: ${Object.keys(shipment).join(', ')}`);

  // Extract shipping cost from the shipment
  let shippingCost = 0;

  // Extract the shipment date
  let shipmentDate = null;

  // Use created_at as the only source for shipment date
  if (shipment.created_at) {
    shipmentDate = new Date(shipment.created_at);
    console.log(`Found created_at: ${shipment.created_at}, parsed as: ${shipmentDate.toISOString()}`);
  } else if (shipment.createDate) {
    // Fallback to createDate if created_at is not available (same field, different naming convention)
    shipmentDate = new Date(shipment.createDate);
    console.log(`Using createDate as fallback: ${shipment.createDate}, parsed as: ${shipmentDate.toISOString()}`);
  }

  // Validate the shipment date
  if (shipmentDate && (isNaN(shipmentDate) || !isFinite(shipmentDate))) {
    console.log(`Invalid shipment date: ${shipmentDate}, using current date instead`);
    shipmentDate = null;
  }

  // Try to get the shipping cost from the shipment labels
  try {
    // Get the shipment ID
    const shipmentId = shipment.shipmentId || shipment.id || shipment.shipment_id;

    if (shipmentId) {
      console.log(`Fetching labels for shipment ID: ${shipmentId}`);

      // Fetch the labels for this shipment using v1 API credentials
      const API_KEY = process.env.SHIPSTATION_V1_KEY;
      const API_SECRET = process.env.SHIPSTATION_V1_SECRET;

      if (!API_KEY || !API_SECRET) {
        throw new Error("ShipStation v1 API key and secret are not configured for labels");
      }

      console.log('Using ShipStation v1 API credentials for labels: [REDACTED]');

      // Set up axios with proper TLS configuration
      const httpsAgent = new https.Agent({
        rejectUnauthorized: true, // Verify SSL certificates
        minVersion: 'TLSv1.1' // Require TLS v1.1 or higher
      });

      // Create Basic Auth header with v1 API key and secret
      const basicAuth = Buffer.from(`${API_KEY}:${API_SECRET}`).toString('base64');

      let labelsResponse;

      // Use Basic Auth with v1 API key and secret
      console.log('Using Basic Auth with v1 API credentials for labels');
      try {
        labelsResponse = await axios.get(`https://ssapi.shipstation.com/shipments/${shipmentId}/labels`, {
          headers: {
            'Authorization': `Basic ${basicAuth}`,
            'Content-Type': 'application/json'
          },
          httpsAgent: httpsAgent
        });
        console.log('Successfully fetched labels with Basic Auth');
      } catch (error) {
        console.error(`Error fetching labels: ${error.message}`);

        // Log more detailed error information
        if (error.response) {
          console.error(`Labels response status: ${error.response.status}`);
          console.error(`Labels response headers: ${JSON.stringify(error.response.headers)}`);
          console.error(`Labels response data: ${JSON.stringify(error.response.data)}`);
        }

        // Try an alternative endpoint format
        try {
          console.log('Trying alternative labels endpoint');
          labelsResponse = await axios.get(`https://ssapi.shipstation.com/labels?shipmentId=${shipmentId}`, {
            headers: {
              'Authorization': `Basic ${basicAuth}`,
              'Content-Type': 'application/json'
            },
            httpsAgent: httpsAgent
          });
          console.log('Successfully fetched labels with alternative endpoint');
        } catch (altError) {
          console.error(`Error with alternative labels endpoint: ${altError.message}`);
          throw altError; // Re-throw to be caught by the outer catch block
        }
      }

      if (labelsResponse.data && labelsResponse.data.labels && labelsResponse.data.labels.length > 0) {
        console.log(`Found ${labelsResponse.data.labels.length} labels for shipment ${shipmentId}`);

        // Sum up the shipping costs from all labels (excluding voided labels)
        for (const label of labelsResponse.data.labels) {
          // Check if the label is voided
          if (label.status && label.status.toLowerCase() === "voided") {
            console.log(`Skipping voided label: ${label.label_id || 'unknown'}`);
            continue;
          }

          // Use carrier_fee as the only source for shipping costs
          if (label.carrier_fee && label.carrier_fee.amount) {
            const labelCost = parseFloat(label.carrier_fee.amount || 0);
            shippingCost += labelCost;
            console.log(`Label ${label.label_id} has carrier_fee: $${labelCost}`);
          }
        }

        console.log(`Total shipping cost from all labels: $${shippingCost}`);
      }
    }
  } catch (labelError) {
    console.error(`Error fetching labels: ${labelError.message}`);

    // Log more detailed error information
    if (labelError.response) {
      // The request was made and the server responded with a status code
      // that falls out of the range of 2xx
      console.error(`Labels response status: ${labelError.response.status}`);
      console.error(`Labels response headers: ${JSON.stringify(labelError.response.headers)}`);
      console.error(`Labels response data: ${JSON.stringify(labelError.response.data)}`);
    } else if (labelError.request) {
      // The request was made but no response was received
      console.error(`No labels response received: ${labelError.request}`);
    } else {
      // Something happened in setting up the request that triggered an Error
      console.error(`Error setting up labels request: ${labelError.message}`);
    }

    // Continue with fallback methods
  }

  // If we couldn't get the shipping cost from labels, check the shipment directly
  if (shippingCost <= 0) {
    // Use carrier_fee as the only source for shipping costs
    if (shipment.carrier_fee && shipment.carrier_fee.amount) {
      shippingCost = parseFloat(shipment.carrier_fee.amount);
      console.log(`Found carrier_fee: ${shippingCost}`);
    }
  }

  console.log(`Extracted shipping cost: $${shippingCost}`);

  if (shippingCost > 0) {
    // Update the shipping costs record with the shipment date
    await updateShippingCost(
      shop,
      storeId,
      shippingCost,
      10, // Default markup percentage
      shipmentDate // Pass the shipment date
    );

    // Also update fulfillment costs with the same shipment date
    await updateFulfillmentCost(
      shop,
      1.50,
      shipmentDate // Pass the shipment date
    );

    console.log(`Updated shipping costs for ${shop}, store ${storeId} with $${shippingCost} and added $1.50 fulfillment cost`);
    if (shipmentDate) {
      console.log(`Assigned to month/year: ${shipmentDate.getMonth()}/${shipmentDate.getFullYear()}`);
    } else {
      console.log(`No shipment date found, assigned to current month/year`);
    }

    return {
      success: true,
      message: `Updated shipping costs for ${shop} with $${shippingCost} and added $1.50 fulfillment cost`
    };
  }

  return {
    success: true,
    message: "No shipping cost to process"
  };
}

// Loader to handle GET requests
export async function loader() {
  return Response.json({
    status: "ShipStation webhook endpoint is available",
    usage: "Configure this URL as a webhook endpoint in your ShipStation account"
  });
}
