/**
 * api.shipstation-order-webhook.jsx
 *
 * This file handles webhooks from ShipStation for order events.
 * It processes new orders to extract carrier fees and updates the database.
 */

import { updateShippingCost, updateFulfillmentCost } from "../models/ShippingCost.server";
import { getStoreMappingByStoreId } from "../models/ShipStationStoreMapping.server";
import { getLastProcessedOrderNumber, updateLastProcessedOrderNumber } from "../models/ShipStationOrderTracking.server";
import { container } from "../lib/container/ServiceContainer.server.js";
import { hasProcessedWebhook, markWebhookProcessed } from "../models/ProcessedWebhook.server";
import axios from "axios";
import https from "https";

export async function action({ request }) {
  if (request.method !== "POST") {
    return Response.json({ error: "Method not allowed" }, { status: 405 });
  }

  try {
    // Check for API key for authentication
    const apiKey = request.headers.get("X-API-Key");
    if (apiKey && apiKey !== process.env.SHIPSTATION_WEBHOOK_KEY) {
      return Response.json({ error: "Invalid API key" }, { status: 401 });
    }

    // Parse the webhook payload
    const payload = await request.json();
    console.log(`Received ShipStation webhook: ${JSON.stringify(payload)}`);

    // Check if this is an order-related webhook
    if (!payload.resource_type || payload.resource_type !== 'ORDER') {
      console.log(`Ignoring non-order webhook: ${payload.resource_type}`);
      return Response.json({
        success: true,
        message: `Ignoring non-order webhook: ${payload.resource_type}`
      });
    }

    // Check if we've already processed this webhook
    const webhookId = payload.resource_id || payload.message_id || payload.id;
    if (!webhookId) {
      console.error('No webhook ID found in payload');
      return Response.json({ error: "No webhook ID found in payload" }, { status: 400 });
    }

    const alreadyProcessed = await hasProcessedWebhook('shipstation', webhookId);
    if (alreadyProcessed) {
      console.log(`Webhook ${webhookId} already processed, skipping`);
      return Response.json({
        success: true,
        message: `Webhook ${webhookId} already processed`
      });
    }

    // Get the order data from the resource_url
    if (!payload.resource_url) {
      console.error('No resource URL found in payload');
      return Response.json({ error: "No resource URL found in payload" }, { status: 400 });
    }

    // Fetch the order data from the resource_url
    console.log(`Fetching order data from ${payload.resource_url}`);

    try {
      // For ShipStation webhooks, we need to use the API key
      const API_KEY = process.env.SHIPSTATION_API_KEY;

      if (!API_KEY) {
        throw new Error("ShipStation API key is not configured");
      }

      console.log('Using ShipStation API key: [REDACTED]');

      // Set up axios with proper TLS configuration
      const httpsAgent = new https.Agent({
        rejectUnauthorized: true, // Verify SSL certificates
        minVersion: 'TLSv1.1' // Require TLS v1.1 or higher
      });

      // Fetch the order data
      const response = await axios.get(payload.resource_url, {
        headers: {
          'API-Key': API_KEY,
          'Content-Type': 'application/json'
        },
        httpsAgent
      });

      // Process the order
      const order = response.data;
      console.log(`Received order data: ${JSON.stringify(order)}`);

      // Get the store ID from the order
      const storeId = order.store_id || order.storeId;
      if (!storeId) {
        console.error('No store ID found in order data');
        return Response.json({ error: "No store ID found in order data" }, { status: 400 });
      }

      // Get the store mapping from the database
      const storeMapping = await getStoreMappingByStoreId(storeId);
      if (!storeMapping) {
        console.error(`No mapping found for store ID: ${storeId}`);
        return Response.json({
          error: `No mapping found for store ID: ${storeId}. Please configure this store ID first.`
        }, { status: 400 });
      }

      const { shop } = storeMapping;

      // Get the last processed order number for this store
      const lastOrderNumber = await getLastProcessedOrderNumber(storeId);
      console.log(`Last processed order number for store ${storeId}: ${lastOrderNumber || 'None'}`);

      // Extract carrier fee information
      const orderNumber = order.order_number;
      // Get services from container
      const shipstationOrderService = container.resolve('shipstationOrderService');

      const carrierFee = shipstationOrderService.extractCarrierFee(order);
      const orderDate = order.order_date ? new Date(order.order_date) : new Date();
      const hasCarrierFee = carrierFee > 0;

      // Parse the order date
      const orderDateObj = new Date(orderDate);
      console.log(`Order date: ${orderDateObj.toISOString()}, carrier fee: $${carrierFee}`);

      // If we have a carrier fee, update the shipping costs
      if (hasCarrierFee && carrierFee > 0) {
        // Update the shipping costs record with the order date
        await updateShippingCost(
          shop,
          storeId,
          carrierFee,
          10, // Default markup percentage
          orderDateObj // Pass the order date
        );

        // Also update fulfillment costs with the same order date
        await updateFulfillmentCost(
          shop,
          1.50,
          orderDateObj // Pass the order date
        );

        console.log(`Updated shipping costs for ${shop} with $${carrierFee} and added $1.50 fulfillment cost`);
      } else {
        console.log(`No carrier fee found for order ${orderNumber}`);
      }

      // Update the last processed order number if this order number is greater
      if (!lastOrderNumber || String(orderNumber) > String(lastOrderNumber)) {
        await updateLastProcessedOrderNumber(storeId, orderNumber, shop);
        console.log(`Updated last processed order number to: ${orderNumber}`);
      }

      // Mark the webhook as processed
      await markWebhookProcessed(shop, 'shipstation', webhookId);

      return Response.json({
        success: true,
        message: hasCarrierFee ?
          `Updated shipping costs for ${shop} with $${carrierFee} and added $1.50 fulfillment cost` :
          "No carrier fee found to process"
      });
    } catch (error) {
      console.error(`Error processing order webhook: ${error.message}`);
      return Response.json({ error: error.message }, { status: 500 });
    }
  } catch (error) {
    console.error(`Error processing webhook: ${error.message}`);
    return Response.json({ error: error.message }, { status: 500 });
  }
}

// Loader to handle GET requests
export async function loader() {
  return Response.json({
    status: "ShipStation order webhook endpoint is available",
    usage: "Configure this URL as a webhook endpoint in your ShipStation account for order events"
  });
}
