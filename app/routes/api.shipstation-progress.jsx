/**
 * api.shipstation-progress.jsx
 *
 * This endpoint provides progress updates during ShipStation data processing
 * via polling.
 */

import { authenticate } from "../shopify.server";

// Global progress tracking object
// This will store progress information for each processing job
if (!global.shipstationProgress) {
  global.shipstationProgress = {};
}

/**
 * Updates the progress for a specific job
 * @param {string} jobId - The unique job identifier
 * @param {object} data - The progress data to update
 */
export function updateShipStationProgress(jobId, data) {
  if (!global.shipstationProgress[jobId]) {
    global.shipstationProgress[jobId] = {
      status: 'initializing',
      message: 'Starting process...',
      progress: 0,
      totalItems: 0,
      processedItems: 0,
      startTime: Date.now(),
      lastUpdate: Date.now(),
      error: null,
      complete: false
    };
  }

  // Update the progress data
  global.shipstationProgress[jobId] = {
    ...global.shipstationProgress[jobId],
    ...data,
    lastUpdate: Date.now()
  };

  console.log(`Progress updated for job ${jobId}: ${JSON.stringify(global.shipstationProgress[jobId])}`);

  // No need to emit events since we're using polling only
}

/**
 * Marks a job as complete
 * @param {string} jobId - The unique job identifier
 * @param {object} finalData - Final data for the job
 */
export function completeShipStationJob(jobId, finalData = {}) {
  if (global.shipstationProgress[jobId]) {
    global.shipstationProgress[jobId] = {
      ...global.shipstationProgress[jobId],
      ...finalData,
      status: 'complete',
      progress: 100,
      complete: true,
      lastUpdate: Date.now()
    };

    console.log(`Job ${jobId} marked as complete`);

    // No need to emit events since we're using polling only

    // Schedule cleanup after 5 minutes
    setTimeout(() => {
      if (global.shipstationProgress[jobId]) {
        delete global.shipstationProgress[jobId];
        console.log(`Cleaned up job ${jobId} data`);
      }

      // No emitters to clean up
    }, 5 * 60 * 1000);
  }
}

/**
 * Marks a job as failed
 * @param {string} jobId - The unique job identifier
 * @param {string} error - Error message
 */
export function failShipStationJob(jobId, error) {
  if (global.shipstationProgress[jobId]) {
    global.shipstationProgress[jobId] = {
      ...global.shipstationProgress[jobId],
      status: 'failed',
      error: error,
      complete: true,
      lastUpdate: Date.now()
    };

    console.log(`Job ${jobId} marked as failed: ${error}`);

    // No need to emit events since we're using polling only

    // Schedule cleanup after 5 minutes
    setTimeout(() => {
      if (global.shipstationProgress[jobId]) {
        delete global.shipstationProgress[jobId];
        console.log(`Cleaned up job ${jobId} data`);
      }

      // No emitters to clean up
    }, 5 * 60 * 1000);
  }
}

// No emitters needed for polling-only implementation

export async function loader({ request }) {
  try {
    // Authenticate the request
    let session;
    try {
      const authResult = await authenticate.admin(request);
      session = authResult.session;
    } catch (authError) {
      console.error(`Authentication error: ${authError.message}`);
      return new Response(JSON.stringify({ error: "Authentication failed" }), {
        status: 401,
        headers: {
          "Content-Type": "application/json"
        }
      });
    }

    if (!session) {
      return new Response(JSON.stringify({ error: "Unauthorized" }), {
        status: 401,
        headers: {
          "Content-Type": "application/json"
        }
      });
    }

    // Parse the URL and get the jobId
    let url;
    try {
      url = new URL(request.url);
    } catch (urlError) {
      console.error(`URL parsing error: ${urlError.message}`);
      return new Response(JSON.stringify({ error: "Invalid URL" }), {
        status: 400,
        headers: {
          "Content-Type": "application/json"
        }
      });
    }

    const jobId = url.searchParams.get('jobId');

    if (!jobId) {
      return new Response(JSON.stringify({ error: "Job ID is required" }), {
        status: 400,
        headers: {
          "Content-Type": "application/json"
        }
      });
    }

    // Return the current progress as a simple JSON response
    const progressData = global.shipstationProgress[jobId] || {
      status: 'not_found',
      message: 'Job not found or not started yet',
      progress: 0
    };

    console.log(`Returning progress data for job ${jobId}:`, progressData);

    return new Response(JSON.stringify(progressData), {
      status: 200,
      headers: {
        "Content-Type": "application/json"
      }
    });
  } catch (error) {
    console.error(`Error in progress tracking endpoint: ${error.message}`);
    console.error(error.stack);

    // Provide more detailed error information
    let errorMessage = error.message;
    let errorDetails = {};

    // Check for specific error types
    if (error instanceof TypeError) {
      errorDetails.type = 'TypeError';
    } else if (error instanceof SyntaxError) {
      errorDetails.type = 'SyntaxError';
    } else if (error instanceof ReferenceError) {
      errorDetails.type = 'ReferenceError';
    }

    return new Response(JSON.stringify({
      error: errorMessage,
      details: errorDetails,
      timestamp: new Date().toISOString()
    }), {
      status: 500,
      headers: {
        "Content-Type": "application/json"
      }
    });
  }
}
