/**
 * Product Processor
 *
 * This processor implements all the detailed product processing logic
 * including all product types, SKU patterns, and business rules.
 */

import { BaseProcessor } from './BaseProcessor.js';
import { container } from '../../../lib/container/ServiceContainer.server.js';
import { BusinessLogicError, ValidationError } from '../../../lib/errors/AppError.js';

/**
 * Product processor that handles all product types
 */
export class ProductProcessor extends BaseProcessor {
  constructor(dependencies = {}) {
    super(dependencies);
    this.dependencies = dependencies;
    this.pricingService = null;
    this.invoiceBalanceService = null;
    this.initialized = false;
  }

  async initialize() {
    if (!this.initialized) {
      this.pricingService = this.dependencies.pricingService || await container.resolve('pricingService');
      this.invoiceBalanceService = this.dependencies.invoiceBalanceService || await container.resolve('invoiceBalanceService');
      this.initialized = true;
    }
  }

  async ensureInitialized() {
    if (!this.initialized) {
      await this.initialize();
    }
  }

  /**
   * Process a line item using legacy business logic
   * @param {object} lineItem - Shopify line item
   * @param {object} orderData - Complete order data
   * @param {string} shopDomain - Shop domain
   * @returns {Promise<object>} - Processing result
   */
  async processLineItem(lineItem, orderData, shopDomain) {
    try {
      await this.ensureInitialized();

      // Extract line item data
      const productType = (lineItem.product_type || '').toLowerCase().replace('private ', '');
      let sku = lineItem.sku || '';
      const variant = (lineItem.variant_title || lineItem.title || '').toLowerCase();
      const quantity = parseInt(lineItem.quantity) || 0;

      // Handle date properly - use order creation date
      const createdAt = orderData.created_at || new Date();
      const { month, year } = this.parseCreatedAt(createdAt);

      // Store original SKU for checking patterns before removing asterisk
      const originalSku = sku;

      // Remove leading asterisk from SKU if present
      if (sku[0] === "*") {
        sku = sku.slice(1);
      }

      const sku_props = sku.split("-");
      let printCode;

      // Skip processing for certain SKU patterns unless specific shops
      // Check original SKU for *F pattern and processed SKU for other patterns
      if (shopDomain !== "american-trigger-pullers.myshopify.com" &&
          shopDomain !== "phaselineco.myshopify.com" &&
          shopDomain !== "phaselineco-fulfillment.myshopify.com") {
        // Skip all *F, F, and PLD items for non-specified shops
        if (originalSku.startsWith("*F") || sku_props[0].toLowerCase() === "f" || sku_props[0].toLowerCase() === "pld") {
          return {
            success: true,
            skipped: true,
            reason: 'SKU pattern excluded for this shop (*F, F, or PLD items not processed for this shop)'
          };
        }
      } else {
        // For specified shops, only process *F items that are actually flags or patches
        if (originalSku.startsWith("*F")) {
          const isValidFlagOrPatch = this.isValidFlagOrPatchItem(originalSku, productType);
          if (!isValidFlagOrPatch) {
            return {
              success: true,
              skipped: true,
              reason: '*F item is not a flag or patch - skipped'
            };
          }
        }
      }

      // Assign printCode based on Department code
      printCode = this.extractPrintCode(sku, sku_props);

      // Process based on product type
      const result = await this.processProductType(
        shopDomain, productType, printCode, sku, variant, quantity, month, year
      );

      // Extract cost of goods from the processing result
      let costOfGoods = 0;
      if (result && !result.skipped && result.balance) {
        costOfGoods = result.balance;
      }

      return {
        success: true,
        processed: true,
        productType,
        sku,
        variant,
        quantity,
        printCode,
        costOfGoods,
        result
      };

    } catch (error) {
      console.error(`Error processing line item: ${error.message}`);

      // Create unprocessable record
      const context = {
        shop: shopDomain,
        createdAt: orderData.created_at || new Date(),
        orderId: orderData.id
      };
      await this.createUnprocessableRecord(lineItem, context, error);

      return {
        success: false,
        costOfGoods: 0,
        error: error.message,
        errorType: error.name || 'ProcessingError'
      };
    }
  }

  /**
   * Extract print code from SKU
   * @param {string} sku - Product SKU
   * @param {Array} sku_props - SKU split by dashes
   * @returns {string} - Print code
   */
  extractPrintCode(sku, sku_props) {
    const lowerSku = sku.toLowerCase();

    if (sku_props[0].toLowerCase() === "dtf") {
      // For performance shirts with para229, extract just the base print code
      if (lowerSku.includes("para229") || lowerSku.includes("229")) {
        try {
          const basePrintCode = sku_props[1].split("-")[0];
          let printCode = String(basePrintCode).trim().toLowerCase();

          // Special handling for para229 items - force to 'sls' if it contains 'sls'
          if (lowerSku.includes("-sls-")) {
            printCode = "sls";
          }
          return printCode;
        } catch (error) {
          return "sls"; // Default to sls for para229 items if extraction fails
        }
      } else {
        // For DTF items, the actual print code is in the second position
        return String(sku_props[1]).trim().toLowerCase();
      }
    } else {
      return String(sku_props[0]).trim().toLowerCase();
    }
  }

  /**
   * Process product based on type
   * @param {string} shop - Shop domain
   * @param {string} productType - Product type
   * @param {string} printCode - Print code
   * @param {string} sku - SKU
   * @param {string} variant - Variant
   * @param {number} quantity - Quantity
   * @param {number} month - Month (0-11)
   * @param {number} year - Year
   * @returns {Promise<object>} - Processing result
   */
  async processProductType(shop, productType, printCode, sku, variant, quantity, month, year) {
    switch (productType) {
      case "archive": case "tee": case "shirt": case "t shirt": case "t-shirt":
      case "youth shirt": case "rgr shirt": case "men's, woman's, shirt":
        return await this.addShirtOrder(shop, printCode, sku, variant, quantity, month, year);

      case "custom shirt":
        return await this.setNewBalance(shop, "custom shirt", quantity, month, year);

      case "performance wear": case "performance long sleeve": case "performance gear":
        return await this.addPerformanceShirtOrder(shop, printCode, sku, variant, quantity, month, year);

      case "work shirt":
        return await this.setNewBalance(shop, "work shirt", quantity, month, year);

      case "tank top": case "tank":
        return await this.addTankOrder(shop, printCode, variant, quantity, month, year);

      case "long sleeve": case "long sleeve shirt":
        return await this.addLongSleeveOrder(shop, printCode, sku, variant, quantity, month, year);

      case "hoodie": case "sweatshirt": case "sweater": case "crewneck":
        return await this.addHoodieOrder(shop, printCode, sku, variant, quantity, month, year);

      case "sticker": case "stickers": case "sticker pack": case "memorial":
        return await this.addStickerOrder(shop, sku, quantity, month, year);

      case "woobie": case "woobie hoodie":
        return await this.addWoobieOrder(shop, variant, quantity, month, year);

      case "canvas": case "print":
        return await this.addCanvasOrder(shop, sku, variant, quantity, month, year);

      case "tumbler": case "flash tumbler":
        return await this.addTumblerOrder(shop, variant, quantity, month, year);

      case "hat": case "headwear": case "tk":
        return await this.addHatOrder(shop, sku, quantity, month, year);

      case "sweatpants":
        return await this.addSweatpantsOrder(shop, variant, quantity, month, year);

      case "outerwear": case "jacket": case "carhartt": case "fleece": case "flannel": case "windbreaker":
        return await this.processOuterwear(shop, sku, variant, quantity, month, year);

      case "polo": case "performance polo":
        return await this.addPoloOrder(shop, variant, quantity, month, year);

      case "silkies": case "shorts": case "panties": case "ranger panties":
        return await this.addSilkiesOrder(shop, variant, quantity, month, year);

      case "bottle": case "water bottle": case "water bottles": case "shaker bottle":
        return await this.addWaterBottleOrder(shop, quantity, month, year);

      case "mug":
        return await this.addMugOrder(shop, sku, quantity, month, year);

      case "glassware": case "pint glass": case "wine glass": case "low ball": case "low ball glass":
        return await this.addGlasswareOrder(shop, sku, quantity, month, year);

      case "decanter":
        return await this.setNewBalance(shop, "decanter", quantity, month, year);

      case "flask": case "flasks":
        return await this.setNewBalance(shop, "flask", quantity, month, year);

      case "bottle opener":
        return await this.setNewBalance(shop, "bottle opener", quantity, month, year);

      case "vanity plate":
        return await this.addVanityPlateOrder(shop, quantity, month, year);

      case "jewelery": case "laser engraving":
        return await this.addJeweleryOrder(shop, sku, variant, quantity, month, year);

      case "duffel": case "bag": case "duffel bag":
        return await this.addBagOrder(shop, sku, quantity, month, year);

      case "accessory": case "humidor":
        return await this.addAccessoryOrder(shop, sku, quantity, month, year);

      case "special box": case "pack":
        return await this.addSpecialBoxOrder(shop, sku, variant, quantity, month, year);

      case "patch": case "patches":
        if (shop === "american-trigger-pullers.myshopify.com" ||
            shop === "phaselineco.myshopify.com" ||
            shop === "phaselineco-fulfillment.myshopify.com") {
          return await this.addPatchOrder(shop, sku, quantity, month, year);
        }
        return { skipped: true, reason: 'Patches not processed for this shop' };

      case "flag":
        if (shop === "american-trigger-pullers.myshopify.com" ||
            shop === "phaselineco.myshopify.com" ||
            shop === "phaselineco-fulfillment.myshopify.com") {
          return await this.addFlagOrder(shop, sku, quantity, month, year);
        }
        return { skipped: true, reason: 'Flags not processed for this shop' };

      case "no discount": case "":
        return await this.processBySku(shop, sku, variant, quantity, month, year);

      case "beanie":
      case "beverage holder":
      case "home decor": case "acrylic print": case "desk mats": case "decal":
      case "whiskey stones": case "kia contribution":
        return { skipped: true, reason: 'Product type not processed' };

      default:
        throw new BusinessLogicError(`Product type ${productType} did not match any known product types`);
    }
  }

  /**
   * Parse created at date
   * @param {Date|string|object} createdAt - Creation date
   * @returns {object} - Parsed month and year
   */
  parseCreatedAt(createdAt) {
    let month, year;

    if (createdAt && typeof createdAt === 'object' && 'month' in createdAt && 'year' in createdAt) {
      month = parseInt(createdAt.month);
      year = parseInt(createdAt.year);
    } else if (createdAt instanceof Date) {
      month = createdAt.getMonth();
      year = createdAt.getFullYear();
    } else if (typeof createdAt === 'string') {
      const createdAtDate = new Date(createdAt);
      month = createdAtDate.getMonth();
      year = createdAtDate.getFullYear();
    } else {
      const currentDate = new Date();
      month = currentDate.getMonth();
      year = currentDate.getFullYear();
    }

    // Validate month and year
    if (typeof month !== 'number' || month < 0 || month > 11) {
      month = new Date().getMonth();
    }
    if (typeof year !== 'number' || year < 2000 || year > 2100) {
      year = new Date().getFullYear();
    }

    return { month, year };
  }

  /**
   * Set new balance for a category
   * @param {string} shop - Shop domain
   * @param {string} category - Product category
   * @param {number} quantity - Quantity
   * @param {number} month - Month (0-11)
   * @param {number} year - Year
   * @returns {Promise<object>} - Balance result
   */
  async setNewBalance(shop, category, quantity, month, year) {
    // Validate inputs
    if (!shop || !category) {
      throw new ValidationError(`Invalid shop or category - shop: ${shop}, category: ${category}`);
    }

    const numQuantity = Number(quantity);
    if (isNaN(numQuantity) || numQuantity <= 0) {
      throw new ValidationError(`Invalid quantity: ${quantity}`);
    }

    if (month < 0 || month > 11 || !Number.isInteger(month)) {
      throw new ValidationError(`Invalid month: ${month}`);
    }

    if (year < 2000 || year > 2100 || !Number.isInteger(year)) {
      throw new ValidationError(`Invalid year: ${year}`);
    }

    // Get or create price
    let price = await this.pricingService.getPriceByCategory(category, shop);
    if (!price) {
      // Import pricing data and create price
      const { default: pricingData } = await import('../../../pricingData/pricing.js');
      if (!pricingData[category]) {
        throw new BusinessLogicError(`No pricing data for category: ${category}`);
      }
      price = await this.pricingService.setPrice({
        shop,
        category,
        cost: pricingData[category],
        sku: null,
        productId: null,
        variantId: null
      });
    }

    // Update invoice balance with retry mechanism
    const MAX_RETRIES = 3;
    let retryCount = 0;
    let success = false;

    while (retryCount < MAX_RETRIES && !success) {
      try {
        const result = await this.invoiceBalanceService.updateBalance({
          shop,
          category,
          month,
          year,
          quantity: numQuantity,
          amount: Number(price.cost) * numQuantity
        });

        success = true;
        return result;
      } catch (error) {
        retryCount++;
        if (retryCount >= MAX_RETRIES) {
          throw new BusinessLogicError(`Failed to update balance after ${MAX_RETRIES} attempts: ${error.message}`);
        }

        // Wait before retrying
        const delay = 100 * retryCount;
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }

  /**
   * Check if variant is oversized and add upcharge
   * @param {string} shop - Shop domain
   * @param {string} variant - Variant title
   * @param {number} quantity - Quantity
   * @param {number} month - Month
   * @param {number} year - Year
   * @returns {Promise<object|null>} - Upcharge result or null
   */
  async checkIfOversize(shop, variant, quantity, month, year) {
    const lowerVariant = variant.toLowerCase();

    if (lowerVariant.includes("2xl")) {
      return await this.setNewBalance(shop, "upcharge 2xl", quantity, month, year);
    } else if (lowerVariant.includes("3xl")) {
      return await this.setNewBalance(shop, "upcharge 3xl", quantity, month, year);
    }
    return null;
  }

  /**
   * Process shirt orders
   * @param {string} shop - Shop domain
   * @param {string} printCode - Print code
   * @param {string} sku - SKU
   * @param {string} variant - Variant
   * @param {number} quantity - Quantity
   * @param {number} month - Month
   * @param {number} year - Year
   * @returns {Promise<object>} - Processing result
   */
  async addShirtOrder(shop, printCode, sku, variant, quantity, month, year) {
    const lowerSku = sku.toLowerCase();

    if (lowerSku.includes("performance") || (lowerSku.includes("para-") && (!lowerSku.includes("nxtlvl6210") && !lowerSku.includes("nxtlvl3312") && !lowerSku.includes("dt6000"))) || lowerSku.includes("st350")) {
      return await this.addPerformanceShirtOrder(shop, printCode, sku, variant, quantity, month, year);
    }

    await this.checkIfOversize(shop, variant, quantity, month, year);

    if (lowerSku.includes("basic")) {
      return await this.setNewBalance(shop, "basic tee", quantity, month, year);
    }

    if (printCode === "ss") {
      if (lowerSku.includes("c1717") || lowerSku.includes("cc1717")) {
        return await this.setNewBalance(shop, "single-sided comfort colors tee", quantity, month, year);
      } else {
        return await this.setNewBalance(shop, "single-sided tee", quantity, month, year);
      }
    } else if (printCode === "ds") {
      if (lowerSku.includes("c1717") || lowerSku.includes("cc1717")) {
        return await this.setNewBalance(shop, "double-sided comfort colors tee", quantity, month, year);
      } else {
        return await this.setNewBalance(shop, "double-sided tee", quantity, month, year);
      }
    } else if (printCode === "ds1s") {
      // Double sided shirt with single sleeve print - price like long sleeve with sleeve print
      if (lowerSku.includes("c1717") || lowerSku.includes("cc1717")) {
        await this.setNewBalance(shop, "double-sided comfort colors tee", quantity, month, year);
      } else {
        await this.setNewBalance(shop, "double-sided tee", quantity, month, year);
      }
      return await this.setNewBalance(shop, "dtf sleeve print", quantity, month, year);
    } else if (printCode === "sls" || printCode === "dls") {
      // Handle long sleeve print codes
      return await this.addLongSleeveOrder(shop, printCode, sku, variant, quantity, month, year);
    } else {
      throw new BusinessLogicError(`The print code ${printCode} does not match any valid tee print codes`);
    }
  }

  /**
   * Process performance shirt orders
   * @param {string} shop - Shop domain
   * @param {string} printCode - Print code
   * @param {string} sku - SKU
   * @param {string} variant - Variant
   * @param {number} quantity - Quantity
   * @param {number} month - Month
   * @param {number} year - Year
   * @returns {Promise<object>} - Processing result
   */
  async addPerformanceShirtOrder(shop, printCode, sku, variant, quantity, month, year) {
    const lowerSku = sku.toLowerCase();

    await this.checkIfOversize(shop, variant, quantity, month, year);

    if (lowerSku.includes("st350")) {
      return await this.setNewBalance(shop, "spor-tek ST350", quantity, month, year);
    } else if (lowerSku.includes("para210") || lowerSku.includes("210") || lowerSku.includes("para214") || lowerSku.includes("214")) {
      if (printCode === "sls" || printCode === "shls") {
        return await this.setNewBalance(shop, "paragon long islander performance long sleeve PARA210/214, single-sided", quantity, month, year);
      } else if (printCode.startsWith("dls") || printCode.startsWith("ls")) {
        return await this.setNewBalance(shop, "paragon long islander performance long sleeve PARA210/214, double-sided", quantity, month, year);
      } else {
        throw new BusinessLogicError("A valid print code was not found for this item (sls, shls, dls, ls, etc.)");
      }
    } else if (lowerSku.includes("para216") || lowerSku.includes("216")) {
      if (printCode === "sls" || printCode === "shls") {
        return await this.setNewBalance(shop, "paragon cayman camo colorblocked performance long sleeve PARA216, single-sided", quantity, month, year);
      } else if (printCode === "dls" || printCode === "ls") {
        return await this.setNewBalance(shop, "paragon cayman camo colorblocked performance long sleeve PARA216, double-sided", quantity, month, year);
      } else {
        throw new BusinessLogicError("A valid print code was not found for this item (sls, shls, dls, ls, etc.)");
      }
    } else if (lowerSku.includes("para220") || lowerSku.includes("220")) {
      if (printCode === "sls") {
        return await this.setNewBalance(shop, "paragon bahama performance hooded long sleeve PARA220, single-sided", quantity, month, year);
      } else if (printCode === "dls" || printCode === "ls") {
        return await this.setNewBalance(shop, "paragon bahama performance hooded long sleeve PARA220, double-sided", quantity, month, year);
      } else {
        throw new BusinessLogicError("A valid print code was not found for this item (sls, shls, dls, ls, etc.)");
      }
    } else if (lowerSku.includes("para225") || lowerSku.includes("225")) {
      if (printCode === "sls") {
        return await this.setNewBalance(shop, "paragon barbados performance pin dot long sleeve PARA225, single-sided", quantity, month, year);
      } else if (printCode === "dls" || printCode === "ls") {
        return await this.setNewBalance(shop, "paragon barbados performance pin dot long sleeve PARA225, double-sided", quantity, month, year);
      } else {
        throw new BusinessLogicError("A valid print code was not found for this item (sls, shls, dls, ls, etc.)");
      }
    } else if (lowerSku.includes("para200") || lowerSku.includes("200-")) {
      if (printCode === "ss") {
        return await this.setNewBalance(shop, "paragon islander performance tee PARA200, single-sided", quantity, month, year);
      } else if (printCode === "ds") {
        return await this.setNewBalance(shop, "paragon islander performance tee PARA200, double-sided", quantity, month, year);
      } else {
        throw new BusinessLogicError("A valid print code was not found for this item (ss, ds, etc.)");
      }
    } else if (lowerSku.includes("para229") || lowerSku.includes("229")) {
      // Force the print code to be a clean string
      const cleanPrintCode = String(printCode).trim();

      if (cleanPrintCode === "sls" || cleanPrintCode === "shls") {
        return await this.setNewBalance(shop, "paragon montauk oceanic fade performance long sleeve PARA229, single-sided", quantity, month, year);
      } else if (cleanPrintCode === "dls" || cleanPrintCode === "ls") {
        return await this.setNewBalance(shop, "paragon montauk oceanic fade performance long sleeve PARA229, double-sided", quantity, month, year);
      } else {
        // Special case for para229 items - if we can't match the print code but it's clearly a para229 item,
        // default based on SKU pattern
        if (sku.toLowerCase().includes("-sls-") || sku.toLowerCase().includes("-shls-")) {
          return await this.setNewBalance(shop, "paragon montauk oceanic fade performance long sleeve PARA229, single-sided", quantity, month, year);
        } else if (sku.toLowerCase().includes("-dls-") || sku.toLowerCase().includes("-ls-")) {
          return await this.setNewBalance(shop, "paragon montauk oceanic fade performance long sleeve PARA229, double-sided", quantity, month, year);
        }

        // Last resort - default to single-sided for para229 items
        return await this.setNewBalance(shop, "paragon montauk oceanic fade performance long sleeve PARA229, single-sided", quantity, month, year);
      }
    } else {
      throw new BusinessLogicError("A valid product was not found for a performance shirt in the item SKU");
    }
  }

  /**
   * Process long sleeve orders
   * @param {string} shop - Shop domain
   * @param {string} printCode - Print code
   * @param {string} sku - SKU
   * @param {string} variant - Variant
   * @param {number} quantity - Quantity
   * @param {number} month - Month
   * @param {number} year - Year
   * @returns {Promise<object>} - Processing result
   */
  async addLongSleeveOrder(shop, printCode, sku, variant, quantity, month, year) {
    const lowerSku = sku.toLowerCase();

    if (lowerSku.includes("performance") || lowerSku.includes("para-")) {
      return await this.addPerformanceShirtOrder(shop, printCode, sku, variant, quantity, month, year);
    }

    await this.checkIfOversize(shop, variant, quantity, month, year);

    switch (printCode) {
      case "sls": case "ss":
        return await this.setNewBalance(shop, "single-sided long sleeve", quantity, month, year);
      case "ls": case "dls": case "ds":
        return await this.setNewBalance(shop, "double-sided long sleeve", quantity, month, year);
      case "sls1s":
        await this.setNewBalance(shop, "single-sided long sleeve", quantity, month, year);
        return await this.setNewBalance(shop, "dtf sleeve print", quantity, month, year);
      case "sls2s":
        await this.setNewBalance(shop, "single-sided long sleeve", quantity, month, year);
        return await this.setNewBalance(shop, "dtf sleeve print", quantity * 2, month, year);
      case "dls1s": case "ls1s":
        await this.setNewBalance(shop, "double-sided long sleeve", quantity, month, year);
        return await this.setNewBalance(shop, "dtf sleeve print", quantity, month, year);
      case "dls2s": case "ls2s":
        await this.setNewBalance(shop, "double-sided long sleeve", quantity, month, year);
        return await this.setNewBalance(shop, "dtf sleeve print", quantity * 2, month, year);
      case "sp":
        return await this.addSpecialBoxOrder(shop, sku, variant, quantity, month, year);
      default:
        throw new BusinessLogicError(`The print code ${printCode} does not match any known print codes (ss, ds, sls, dls, etc.)`);
    }
  }

  /**
   * Process tank orders
   * @param {string} shop - Shop domain
   * @param {string} printCode - Print code
   * @param {string} variant - Variant
   * @param {number} quantity - Quantity
   * @param {number} month - Month
   * @param {number} year - Year
   * @returns {Promise<object>} - Processing result
   */
  async addTankOrder(shop, printCode, variant, quantity, month, year) {
    await this.checkIfOversize(shop, variant, quantity, month, year);

    if (printCode === "ss") {
      return await this.setNewBalance(shop, "single-sided tank", quantity, month, year);
    } else if (printCode === "ds") {
      return await this.setNewBalance(shop, "double-sided tank", quantity, month, year);
    } else {
      throw new BusinessLogicError(`The print code ${printCode} does not match any valid tank top print codes`);
    }
  }

  /**
   * Check if variant contains camo
   * @param {string} variant - Variant title
   * @returns {boolean} - True if contains camo
   */
  containsCamo(variant) {
    return variant.toLowerCase().includes('camo');
  }

  /**
   * Process hoodie orders
   * @param {string} shop - Shop domain
   * @param {string} printCode - Print code
   * @param {string} sku - SKU
   * @param {string} variant - Variant
   * @param {number} quantity - Quantity
   * @param {number} month - Month
   * @param {number} year - Year
   * @returns {Promise<object>} - Processing result
   */
  async addHoodieOrder(shop, printCode, sku, variant, quantity, month, year) {
    const lowerSku = sku.toLowerCase();

    await this.checkIfOversize(shop, variant, quantity, month, year);

    if (lowerSku.includes("ind5000")) {
      return await this.setNewBalance(shop, "cross grain hoodie IND5000P", quantity, month, year);
    } else if (lowerSku.includes("cc1566")) {
      return await this.setNewBalance(shop, "comfort colors garment-dyed sweatshirt", quantity, month, year);
    } else {
      // Embroidery Hoodies are always single sided
      if (lowerSku[0] === "e") {
        if (this.containsCamo(variant)) {
          return await this.setNewBalance(shop, "single-sided camo hoodie", quantity, month, year);
        } else {
          return await this.setNewBalance(shop, "single-sided hoodie/sweatshirt", quantity, month, year);
        }
      }

      switch (printCode) {
        case "sh":
          if (this.containsCamo(variant)) {
            return await this.setNewBalance(shop, "single-sided camo hoodie", quantity, month, year);
          } else {
            return await this.setNewBalance(shop, "single-sided hoodie/sweatshirt", quantity, month, year);
          }
        case "dh":
          if (this.containsCamo(variant)) {
            return await this.setNewBalance(shop, "double-sided camo hoodie", quantity, month, year);
          } else {
            return await this.setNewBalance(shop, "double-sided hoodie/sweatshirt", quantity, month, year);
          }
        case "sh1s":
          if (this.containsCamo(variant)) {
            await this.setNewBalance(shop, "single-sided camo hoodie", quantity, month, year);
            return await this.setNewBalance(shop, "dtf sleeve print", quantity, month, year);
          } else {
            await this.setNewBalance(shop, "single-sided hoodie/sweatshirt", quantity, month, year);
            return await this.setNewBalance(shop, "dtf sleeve print", quantity, month, year);
          }
        case "sh2s":
          if (this.containsCamo(variant)) {
            await this.setNewBalance(shop, "single-sided camo hoodie", quantity, month, year);
            return await this.setNewBalance(shop, "dtf sleeve print", quantity * 2, month, year);
          } else {
            await this.setNewBalance(shop, "single-sided hoodie/sweatshirt", quantity, month, year);
            return await this.setNewBalance(shop, "dtf sleeve print", quantity * 2, month, year);
          }
        case "dh1s":
          if (this.containsCamo(variant)) {
            await this.setNewBalance(shop, "double-sided camo hoodie", quantity, month, year);
            return await this.setNewBalance(shop, "dtf sleeve print", quantity, month, year);
          } else {
            await this.setNewBalance(shop, "double-sided hoodie/sweatshirt", quantity, month, year);
            return await this.setNewBalance(shop, "dtf sleeve print", quantity, month, year);
          }
        case "dh2s":
          if (this.containsCamo(variant)) {
            await this.setNewBalance(shop, "double-sided camo hoodie", quantity, month, year);
            return await this.setNewBalance(shop, "dtf sleeve print", quantity * 2, month, year);
          } else {
            await this.setNewBalance(shop, "double-sided hoodie/sweatshirt", quantity, month, year);
            return await this.setNewBalance(shop, "dtf sleeve print", quantity * 2, month, year);
          }
        default:
          throw new BusinessLogicError(`The print code ${printCode} does not match any valid hoodie print codes`);
      }
    }
  }

  /**
   * Process sticker orders
   * @param {string} shop - Shop domain
   * @param {string} sku - SKU
   * @param {number} quantity - Quantity
   * @param {number} month - Month
   * @param {number} year - Year
   * @returns {Promise<object>} - Processing result
   */
  async addStickerOrder(shop, sku, quantity, month, year) {
    const lowerSku = sku.toLowerCase();

    if (lowerSku.includes("pack")) {
      return await this.setNewBalance(shop, "sticker pack", quantity, month, year);
    } else {
      return await this.setNewBalance(shop, "sticker", quantity, month, year);
    }
  }

  /**
   * Process woobie orders
   * @param {string} shop - Shop domain
   * @param {string} variant - Variant
   * @param {number} quantity - Quantity
   * @param {number} month - Month
   * @param {number} year - Year
   * @returns {Promise<object>} - Processing result
   */
  async addWoobieOrder(shop, variant, quantity, month, year) {
    await this.checkIfOversize(shop, variant, quantity, month, year);
    return await this.setNewBalance(shop, "woobie hoodie", quantity, month, year);
  }

  /**
   * Process canvas orders
   * @param {string} shop - Shop domain
   * @param {string} sku - SKU
   * @param {string} variant - Variant
   * @param {number} quantity - Quantity
   * @param {number} month - Month
   * @param {number} year - Year
   * @returns {Promise<object>} - Processing result
   */
  async addCanvasOrder(shop, sku, variant, quantity, month, year) {
    let category;

    // Convert to lowercase for case-insensitive matching
    const lowerSku = sku.toLowerCase();
    const lowerVariant = variant.toLowerCase();

    if (lowerSku.includes("18x24")) {
      if (lowerSku.includes("stretched")) {
        category = "canvas 18x24 stretched";
      } else if (lowerSku.includes("loose")) {
        category = "canvas 18x24 loose";
      } else {
        throw new BusinessLogicError("Cannot determine if canvas is loose or stretched");
      }
    } else if (lowerSku.includes("20x28")) {
      category = "canvas 20x28 loose";
    } else if (lowerSku.includes("24x36")) {
      if (lowerSku.includes("stretched")) {
        category = "canvas 24x36 stretched";
      } else if (lowerSku.includes("loose")) {
        category = "canvas 24x36 loose";
      } else {
        throw new BusinessLogicError("Cannot determine if canvas is loose or stretched");
      }
    } else if (lowerSku.startsWith("c") || lowerSku.startsWith("*c")) {
      if (lowerVariant.includes("18\" x 24\"") || lowerVariant.includes("18\"x24\"")) {
        if (lowerVariant.includes("stretched")) {
          category = "canvas 18x24 stretched";
        } else if (lowerVariant.includes("loose")) {
          category = "canvas 18x24 loose";
        } else {
          throw new BusinessLogicError("Cannot determine if canvas is loose or stretched");
        }
      } else if (lowerVariant.includes("20\" x 28\"") || lowerVariant.includes("20\"x28\"")) {
        category = "canvas 20x28 loose";
      } else if (lowerVariant.includes("24\" x 36\"") || lowerVariant.includes("24\"x36\"")) {
        if (lowerVariant.includes("stretched")) {
          category = "canvas 24x36 stretched";
        } else if (lowerVariant.includes("loose")) {
          category = "canvas 24x36 loose";
        } else {
          throw new BusinessLogicError("Cannot determine if canvas is loose or stretched");
        }
      } else {
        throw new BusinessLogicError("No valid canvas size was found in the item variant");
      }
    } else {
      throw new BusinessLogicError("No valid canvas size was found in the item SKU");
    }

    return await this.setNewBalance(shop, category, quantity, month, year);
  }

  /**
   * Process tumbler orders
   * @param {string} shop - Shop domain
   * @param {string} variant - Variant
   * @param {number} quantity - Quantity
   * @param {number} month - Month
   * @param {number} year - Year
   * @returns {Promise<object>} - Processing result
   */
  async addTumblerOrder(shop, variant, quantity, month, year) {
    const lowerVariant = variant.toLowerCase();

    if (lowerVariant.includes("20oz") || lowerVariant.includes("20 oz")) {
      return await this.setNewBalance(shop, "tumbler, 20oz", quantity, month, year);
    } else if (lowerVariant.includes("30oz") || lowerVariant.includes("30 oz")) {
      return await this.setNewBalance(shop, "tumbler, 30oz", quantity, month, year);
    } else {
      throw new BusinessLogicError("No valid tumbler size was found in the item variant");
    }
  }

  /**
   * Process hat orders
   * @param {string} shop - Shop domain
   * @param {string} sku - SKU
   * @param {number} quantity - Quantity
   * @param {number} month - Month
   * @param {number} year - Year
   * @returns {Promise<object>} - Processing result
   */
  async addHatOrder(shop, sku, quantity, month, year) {
    const lowerSku = sku.toLowerCase();

    if (lowerSku.includes("rempa")) {
      return await this.setNewBalance(shop, "rempa hat", quantity, month, year);
    } else if (lowerSku.includes("1287")) {
      return await this.setNewBalance(shop, "imperial trucker cap 1287", quantity, month, year);
    } else {
      return await this.setNewBalance(shop, "standard hat", quantity, month, year);
    }
  }

  /**
   * Process sweatpants orders
   * @param {string} shop - Shop domain
   * @param {string} variant - Variant
   * @param {number} quantity - Quantity
   * @param {number} month - Month
   * @param {number} year - Year
   * @returns {Promise<object>} - Processing result
   */
  async addSweatpantsOrder(shop, variant, quantity, month, year) {
    await this.checkIfOversize(shop, variant, quantity, month, year);
    return await this.setNewBalance(shop, "sweatpants IND20PNT", quantity, month, year);
  }

  /**
   * Process outerwear orders
   * @param {string} shop - Shop domain
   * @param {string} sku - SKU
   * @param {string} variant - Variant
   * @param {number} quantity - Quantity
   * @param {number} month - Month
   * @param {number} year - Year
   * @returns {Promise<object>} - Processing result
   */
  async processOuterwear(shop, sku, variant, quantity, month, year) {
    const lowerSku = sku.toLowerCase();

    if (lowerSku.includes("w668") || lowerSku.includes("burnside") || lowerSku.includes("8210")) {
      return await this.addFlannelOrder(shop, sku, variant, quantity, month, year);
    } else if (lowerSku.includes("exp94naw") || lowerSku.includes("anorak")) {
      return await this.addAnorakOrder(shop, variant, quantity, month, year);
    } else if (lowerSku.includes("exp54lwz") || lowerSku.includes("windbreaker")) {
      return await this.addWindbreakerOrder(shop, sku, variant, quantity, month, year);
    } else {
      return await this.addJacketOrder(shop, sku, variant, quantity, month, year);
    }
  }

  /**
   * Process flannel orders
   * @param {string} shop - Shop domain
   * @param {string} sku - SKU
   * @param {string} variant - Variant
   * @param {number} quantity - Quantity
   * @param {number} month - Month
   * @param {number} year - Year
   * @returns {Promise<object>} - Processing result
   */
  async addFlannelOrder(shop, sku, variant, quantity, month, year) {
    const lowerSku = sku.toLowerCase();

    await this.checkIfOversize(shop, variant, quantity, month, year);

    if (lowerSku.includes("w668")) {
      return await this.setNewBalance(shop, "flannel w668", quantity, month, year);
    } else if (lowerSku.includes("burnside") || lowerSku.includes("8210")) {
      return await this.setNewBalance(shop, "flannel burnside 8210", quantity, month, year);
    } else {
      throw new BusinessLogicError("Could not find a valid garment code for flannel in item SKU");
    }
  }

  /**
   * Process anorak orders
   * @param {string} shop - Shop domain
   * @param {string} variant - Variant
   * @param {number} quantity - Quantity
   * @param {number} month - Month
   * @param {number} year - Year
   * @returns {Promise<object>} - Processing result
   */
  async addAnorakOrder(shop, variant, quantity, month, year) {
    await this.checkIfOversize(shop, variant, quantity, month, year);
    return await this.setNewBalance(shop, "anorack EXP94NAW, embroidery", quantity, month, year);
  }

  /**
   * Process windbreaker orders
   * @param {string} shop - Shop domain
   * @param {string} sku - SKU
   * @param {string} variant - Variant
   * @param {number} quantity - Quantity
   * @param {number} month - Month
   * @param {number} year - Year
   * @returns {Promise<object>} - Processing result
   */
  async addWindbreakerOrder(shop, sku, variant, quantity, month, year) {
    const lowerSku = sku.toLowerCase();

    await this.checkIfOversize(shop, variant, quantity, month, year);

    if (lowerSku[0] === "e") {
      return await this.setNewBalance(shop, "windbreaker, embroidery", quantity, month, year);
    } else {
      return await this.setNewBalance(shop, "windbreaker, dtf", quantity, month, year);
    }
  }

  /**
   * Process jacket orders
   * @param {string} shop - Shop domain
   * @param {string} sku - SKU
   * @param {string} variant - Variant
   * @param {number} quantity - Quantity
   * @param {number} month - Month
   * @param {number} year - Year
   * @returns {Promise<object>} - Processing result
   */
  async addJacketOrder(shop, sku, variant, quantity, month, year) {
    const lowerSku = sku.toLowerCase();

    await this.checkIfOversize(shop, variant, quantity, month, year);

    if (lowerSku.includes("softshell") || lowerSku.includes("soft-shell")) {
      return await this.setNewBalance(shop, "soft shell jacket", quantity, month, year);
    } else if (lowerSku.includes("eb250")) {
      return await this.setNewBalance(shop, "eddie bauer fleece EB250", quantity, month, year);
    } else if (lowerSku.includes("ctj131")) {
      return await this.setNewBalance(shop, "carhartt duck active jacket CTJ131", quantity, month, year);
    } else if (lowerSku.includes("driduck") && (lowerSku.includes("7353") || lowerSku.includes("7356"))) {
      return await this.setNewBalance(shop, "dri duck fleece pullover 7353/7356", quantity, month, year);
    } else if (lowerSku.includes("ct103828")) {
      return await this.setNewBalance(shop, "carahrtt duck detroit jacket CT103828", quantity, month, year);
    } else if (lowerSku.includes("ctsj140")) {
      return await this.setNewBalance(shop, "carharrt thermal lined duck jacket CTSJ140", quantity, month, year);
    } else {
      throw new BusinessLogicError("Could not find a valid garment code for jacket in item SKU");
    }
  }

  /**
   * Process polo orders
   * @param {string} shop - Shop domain
   * @param {string} variant - Variant
   * @param {number} quantity - Quantity
   * @param {number} month - Month
   * @param {number} year - Year
   * @returns {Promise<object>} - Processing result
   */
  async addPoloOrder(shop, variant, quantity, month, year) {
    await this.checkIfOversize(shop, variant, quantity, month, year);
    return await this.setNewBalance(shop, "polo", quantity, month, year);
  }

  /**
   * Process silkies orders
   * @param {string} shop - Shop domain
   * @param {string} variant - Variant
   * @param {number} quantity - Quantity
   * @param {number} month - Month
   * @param {number} year - Year
   * @returns {Promise<object>} - Processing result
   */
  async addSilkiesOrder(shop, variant, quantity, month, year) {
    await this.checkIfOversize(shop, variant, quantity, month, year);
    return await this.setNewBalance(shop, "silkies", quantity, month, year);
  }

  /**
   * Process water bottle orders
   * @param {string} shop - Shop domain
   * @param {number} quantity - Quantity
   * @param {number} month - Month
   * @param {number} year - Year
   * @returns {Promise<object>} - Processing result
   */
  async addWaterBottleOrder(shop, quantity, month, year) {
    return await this.setNewBalance(shop, "water bottle", quantity, month, year);
  }

  /**
   * Process mug orders
   * @param {string} shop - Shop domain
   * @param {string} sku - SKU
   * @param {number} quantity - Quantity
   * @param {number} month - Month
   * @param {number} year - Year
   * @returns {Promise<object>} - Processing result
   */
  async addMugOrder(shop, sku, quantity, month, year) {
    const lowerSku = sku.toLowerCase();

    if (lowerSku.includes("11")) {
      return await this.setNewBalance(shop, "mug, 11oz", quantity, month, year);
    } else if (lowerSku.includes("15")) {
      return await this.setNewBalance(shop, "mug, 15oz", quantity, month, year);
    } else {
      throw new BusinessLogicError("No valid size for mug could be found in item SKU");
    }
  }

  /**
   * Process glassware orders
   * @param {string} shop - Shop domain
   * @param {string} sku - SKU
   * @param {number} quantity - Quantity
   * @param {number} month - Month
   * @param {number} year - Year
   * @returns {Promise<object>} - Processing result
   */
  async addGlasswareOrder(shop, sku, quantity, month, year) {
    const lowerSku = sku.toLowerCase();

    if (lowerSku.includes("low-ball") || lowerSku.includes("lowball") || lowerSku.includes("rocks")) {
      if (lowerSku.includes("decanter")) {
        await this.setNewBalance(shop, "decanter", quantity, month, year);
      }
      return await this.setNewBalance(shop, "low ball glass", Number(quantity) * 2, month, year);
    } else if (lowerSku.includes("wine-glass")) {
      return await this.setNewBalance(shop, "wine glass", Number(quantity) * 2, month, year);
    } else if (lowerSku.includes("pint")) {
      return await this.setNewBalance(shop, "pint glass", Number(quantity) * 2, month, year);
    } else if (lowerSku.includes("decanter")) {
      return await this.setNewBalance(shop, "decanter", quantity, month, year);
    } else {
      throw new BusinessLogicError("No valid glassware type was located in item SKU");
    }
  }

  /**
   * Process vanity plate orders
   * @param {string} shop - Shop domain
   * @param {number} quantity - Quantity
   * @param {number} month - Month
   * @param {number} year - Year
   * @returns {Promise<object>} - Processing result
   */
  async addVanityPlateOrder(shop, quantity, month, year) {
    return await this.setNewBalance(shop, "license plate", quantity, month, year);
  }

  /**
   * Process jewelry orders
   * @param {string} shop - Shop domain
   * @param {string} sku - SKU
   * @param {string} variant - Variant
   * @param {number} quantity - Quantity
   * @param {number} month - Month
   * @param {number} year - Year
   * @returns {Promise<object>} - Processing result
   */
  async addJeweleryOrder(shop, sku, variant, quantity, month, year) {
    const lowerSku = sku.toLowerCase();
    const lowerVariant = variant.toLowerCase();

    if (lowerSku.includes("bracelet") || lowerVariant.includes("bracelet")) {
      return await this.setNewBalance(shop, "bracelet", quantity, month, year);
    } else {
      throw new BusinessLogicError("Could not find a valid code for jewelry item in item SKU");
    }
  }

  /**
   * Process bag orders
   * @param {string} shop - Shop domain
   * @param {string} sku - SKU
   * @param {number} quantity - Quantity
   * @param {number} month - Month
   * @param {number} year - Year
   * @returns {Promise<object>} - Processing result
   */
  async addBagOrder(shop, sku, quantity, month, year) {
    const lowerSku = sku.toLowerCase();

    if (lowerSku.includes("duffel") || lowerSku.includes("inddufbag")) {
      return await this.setNewBalance(shop, "duffel bag INDDUFBAG", quantity, month, year);
    } else if (lowerSku.includes("rockwell") || lowerSku.includes("411072")) {
      return await this.setNewBalance(shop, "ogio rockwell pack 411072", quantity, month, year);
    } else {
      throw new BusinessLogicError("No valid item code for bag could be found in item SKU");
    }
  }

  /**
   * Process accessory orders
   * @param {string} shop - Shop domain
   * @param {string} sku - SKU
   * @param {number} quantity - Quantity
   * @param {number} month - Month
   * @param {number} year - Year
   * @returns {Promise<object>} - Processing result
   */
  async addAccessoryOrder(shop, sku, quantity, month, year) {
    const lowerSku = sku.toLowerCase();

    if (lowerSku.includes("humidor")) {
      return await this.setNewBalance(shop, "humidor", quantity, month, year);
    }
    return { skipped: true, reason: 'Accessory type not recognized' };
  }

  /**
   * Process special box orders
   * @param {string} shop - Shop domain
   * @param {string} sku - SKU
   * @param {string} variant - Variant
   * @param {number} quantity - Quantity
   * @param {number} month - Month
   * @param {number} year - Year
   * @returns {Promise<object>} - Processing result
   */
  async addSpecialBoxOrder(shop, sku, variant, quantity, month, year) {
    const lowerSku = sku.toLowerCase();

    if (lowerSku.includes("harrell")) {
      await this.checkIfOversize(shop, variant, quantity * 3, month, year);
      await this.setNewBalance(shop, "single-sided tee", quantity, month, year);
      await this.setNewBalance(shop, "double-sided long sleeve", quantity, month, year);
      await this.setNewBalance(shop, "double-sided tee", quantity, month, year);
      await this.setNewBalance(shop, "standard hat", quantity, month, year);
      return await this.setNewBalance(shop, "sticker pack", quantity, month, year);
    } else if (shop === "american-trigger-pullers.myshopify.com") {
      if (lowerSku.slice(0, 2) === "sp") {
        if (lowerSku.includes("crewneck")) {
          await this.checkIfOversize(shop, variant, Number(quantity) * 5, month, year);
          return await this.setNewBalance(shop, "basic tee", Number(quantity) * 5, month, year);
        } else {
          await this.checkIfOversize(shop, variant, Number(quantity) * 3, month, year);
          return await this.setNewBalance(shop, "single-sided tee", Number(quantity) * 3, month, year);
        }
      } else if (lowerSku.includes("muleskinners") || lowerSku.includes("blacksmiths")) {
        await this.checkIfOversize(shop, variant, Number(quantity) * 4, month, year);
        await this.setNewBalance(shop, "double-sided tee", Number(quantity) * 2, month, year);
        await this.setNewBalance(shop, "double-sided long sleeve", quantity, month, year);
        return await this.setNewBalance(shop, "double-sided hoodie/sweatshirt", quantity, month, year);
      } else if (lowerSku.includes("anarchy") || lowerSku.includes("ghost")) {
        await this.checkIfOversize(shop, variant, quantity * 5, month, year);
        await this.setNewBalance(shop, "double-sided tee", quantity * 3, month, year);
        await this.setNewBalance(shop, "double-sided long sleeve", quantity, month, year);
        return await this.setNewBalance(shop, "double-sided hoodie/sweatshirt", quantity, month, year);
      } else {
        throw new BusinessLogicError("Could not find a matching pack for the item SKU");
      }
    } else {
      throw new BusinessLogicError("Could not find a matching pack for the item SKU");
    }
  }

  /**
   * Process patch orders
   * @param {string} shop - Shop domain
   * @param {string} sku - SKU
   * @param {number} quantity - Quantity
   * @param {number} month - Month
   * @param {number} year - Year
   * @returns {Promise<object>} - Processing result
   */
  async addPatchOrder(shop, _sku, quantity, month, year) {
    return await this.setNewBalance(shop, "patch", quantity, month, year);
  }

  /**
   * Process flag orders
   * @param {string} shop - Shop domain
   * @param {string} sku - SKU
   * @param {number} quantity - Quantity
   * @param {number} month - Month
   * @param {number} year - Year
   * @returns {Promise<object>} - Processing result
   */
  async addFlagOrder(shop, sku, quantity, month, year) {
    const lowerSku = sku.toLowerCase();

    // Determine if flag is single-sided or double-sided based on SKU
    let flagCategory;
    if (lowerSku.includes("single-sided") || lowerSku.includes("single_sided")) {
      flagCategory = "single-sided flag";
    } else if (lowerSku.includes("double-sided") || lowerSku.includes("double_sided")) {
      flagCategory = "double-sided flag";
    } else {
      // Default to single-sided if not specified (most common case)
      flagCategory = "single-sided flag";
    }

    return await this.setNewBalance(shop, flagCategory, quantity, month, year);
  }

  /**
   * Process by SKU when product type is unknown
   * @param {string} shop - Shop domain
   * @param {string} sku - SKU
   * @param {string} variant - Variant
   * @param {number} quantity - Quantity
   * @param {number} month - Month
   * @param {number} year - Year
   * @returns {Promise<object>} - Processing result
   */
  async processBySku(shop, sku, variant, quantity, month, year) {
    const sku_props = sku.split("-");
    const department = sku_props[0];

    // Determine the print code based on the department
    let printCode = "";
    if (department.toLowerCase() === "dtf" || department.toLowerCase() === "eh" || department.toLowerCase() === "lh" || department.toLowerCase() === "ej") {
      // For DTF, EH, LH, and EJ departments, the print code is the second part
      printCode = sku_props.length > 1 ? sku_props[1] : "";
    } else {
      // For other departments, the print code is the first part
      printCode = department;
    }

    switch (department.toLowerCase()) {
      case "eh": case "lh":
        return await this.addHatOrder(shop, sku, quantity, month, year);
      case "ej":
        return await this.setNewBalance(shop, "work shirt", quantity, month, year);
      case "dtf":
        switch (printCode) {
          case "ds": case "ss": case "z": case "ds1s":
            return await this.addShirtOrder(shop, printCode, sku, variant, quantity, month, year);
          case "dls": case "sls": case "dls1s": case "dls2s": case "sls1s": case "sls2s": case "ls": case "ls1s": case "ls2s":
            return await this.addLongSleeveOrder(shop, printCode, sku, variant, quantity, month, year);
          case "sh": case "dh": case "sh1s": case "sh2s": case "dh1s": case "dh2s":
            return await this.addHoodieOrder(shop, printCode, sku, variant, quantity, month, year);
          default:
            throw new BusinessLogicError(`Unknown DTF print code: ${printCode}`);
        }
      case "s":
        return await this.addStickerOrder(shop, sku, quantity, month, year);
      case "pld":
        // For specific shops, process patches with 'pld' prefix
        if (shop === "american-trigger-pullers.myshopify.com" || shop === "phaselineco.myshopify.com") {
          return await this.addPatchOrder(shop, sku, quantity, month, year);
        } else {
          return { skipped: true, reason: 'PLD department not processed for this shop' };
        }
      case "f":
        // For specific shops, process flags and patches with 'f' prefix
        if (shop === "american-trigger-pullers.myshopify.com" || shop === "phaselineco.myshopify.com") {
          return await this.addFlagOrder(shop, sku, quantity, month, year);
        } else {
          return { skipped: true, reason: 'F department not processed for this shop' };
        }
      default:
        throw new BusinessLogicError(`Unknown department: ${department}`);
    }
  }

  /**
   * Check if a *F item is actually a flag or patch that should be processed
   * @param {string} originalSku - Original SKU with asterisk
   * @param {string} productType - Product type from Shopify
   * @returns {boolean} - True if it's a valid flag or patch item
   */
  isValidFlagOrPatchItem(originalSku, productType) {
    // Check product type first
    if (productType && (productType.toLowerCase() === "flag" || productType.toLowerCase() === "patch" || productType.toLowerCase() === "patches")) {
      return true;
    }

    // Check SKU patterns for flags and patches
    const lowerSku = originalSku.toLowerCase();

    // Flag patterns
    if (lowerSku.includes("-flag") || lowerSku.includes("flag-")) {
      return true;
    }

    // Patch patterns
    if (lowerSku.includes("-patch") || lowerSku.includes("patch-")) {
      return true;
    }

    // If none of the patterns match, it's not a flag or patch
    return false;
  }

  /**
   * Create unprocessable record
   * @param {object} lineItem - Line item data
   * @param {object} context - Processing context
   * @param {Error} error - Processing error
   * @returns {Promise<void>}
   */
  async createUnprocessableRecord(lineItem, context, error) {
    try {
      const { shop, createdAt } = context;

      let errorField;
      switch (error.name) {
        case "ProductNotFoundError":
          errorField = "Product Type";
          break;
        case "InvalidProductError":
          errorField = "SKU";
          break;
        case "PrintCodeError":
          errorField = "Print Code";
          break;
        case "InvalidSizeError":
          errorField = "Variant";
          break;
        default:
          errorField = "Unknown";
          break;
      }

      // Create unprocessable record using Prisma directly
      await this.prisma.unprocessable.create({
        data: {
          shop,
          productType: lineItem.product_type || '',
          variant: lineItem.variant_title || lineItem.title || '',
          sku: lineItem.sku || '',
          quantity: parseInt(lineItem.quantity) || 0,
          date: createdAt instanceof Date ? createdAt : new Date(createdAt || Date.now()),
          errorField,
          message: error.message,
          variantId: lineItem.variant_id?.toString() || null,
          productId: lineItem.product_id?.toString() || null
        }
      });
    } catch (dbError) {
      console.error('Error creating unprocessable record:', dbError);
    }
  }
}
