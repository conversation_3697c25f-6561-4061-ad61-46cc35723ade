import "@shopify/shopify-app-remix/adapters/node";
import {
  ApiVersion,
  AppDistribution,
  shopifyApp,
  LogSeverity,
} from "@shopify/shopify-app-remix/server";
import { PrismaSessionStorage } from "@shopify/shopify-app-session-storage-prisma";
import { restResources } from "@shopify/shopify-api/rest/admin/2025-01";
import prisma from "./db.server";
import { config } from "./lib/config/index.js";

// Silence deprecation warnings by overriding the console.warn method
const originalWarn = console.warn;
console.warn = function(message, ...args) {
  // Skip logging if the message contains deprecation notices
  if (typeof message === 'string' &&
      (message.includes('API Deprecation Notice') ||
       message.includes('shopify-api/WARNING') ||
       message.toLowerCase().includes('deprecated') ||
       message.toLowerCase().includes('deprecation'))) {
    return; // Silently ignore deprecation warnings
  }

  // Log all other warnings normally
  originalWarn.apply(console, [message, ...args]);
};

// Function to get default store configuration from multistore config
function getDefaultStoreCredentials() {
  // Try to use standard environment variables first
  if (process.env.SHOPIFY_API_KEY && process.env.SHOPIFY_API_SECRET) {
    return {
      apiKey: process.env.SHOPIFY_API_KEY,
      apiSecret: process.env.SHOPIFY_API_SECRET,
    };
  }

  // Fallback to first available store from multistore configuration
  const supportedStores = config.multistore.supportedStores;
  const storeConfigs = config.multistore.storeConfigs;

  if (supportedStores.length > 0) {
    const firstStore = supportedStores[0];
    const storeConfig = storeConfigs[firstStore];

    if (storeConfig && storeConfig.apiKey && storeConfig.apiSecret) {
      console.log(`[Shopify Server] Using multistore credentials for default store: ${firstStore}`);
      return {
        apiKey: storeConfig.apiKey,
        apiSecret: storeConfig.apiSecret,
      };
    }
  }

  // If no valid configuration found, throw error
  throw new Error(
    'No valid Shopify API credentials found. Please set SHOPIFY_API_KEY and SHOPIFY_API_SECRET environment variables, or ensure multistore configuration is properly set up.'
  );
}

// Get credentials using fallback mechanism
const credentials = getDefaultStoreCredentials();

const shopify = shopifyApp({
  apiKey: credentials.apiKey,
  apiSecretKey: credentials.apiSecret,
  apiVersion: ApiVersion.January25,
  scopes: process.env.SCOPES?.split(",") || config.shopify.scopes,
  appUrl: process.env.SHOPIFY_APP_URL || config.app.appUrl,
  authPathPrefix: "/auth",
  sessionStorage: new PrismaSessionStorage(prisma),
  distribution: AppDistribution.AppStore,
  restResources,
  future: {
    unstable_newEmbeddedAuthStrategy: true,
    removeRest: false, // Re-enable REST API
  },
  logger: {
    level: LogSeverity.Error, // Only log errors, not warnings
  },
  ...(process.env.SHOP_CUSTOM_DOMAIN
    ? { customShopDomains: [process.env.SHOP_CUSTOM_DOMAIN] }
    : {}),
});

export default shopify;
export const apiVersion = ApiVersion.January25;
export const addDocumentResponseHeaders = shopify.addDocumentResponseHeaders;
export const authenticate = shopify.authenticate;
export const unauthenticated = shopify.unauthenticated;
export const login = shopify.login;
export const registerWebhooks = shopify.registerWebhooks;
export const sessionStorage = shopify.sessionStorage;
